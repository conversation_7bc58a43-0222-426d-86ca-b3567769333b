import React, { createContext, useState, useContext } from 'react';
import { collection, addDoc,  getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '../Config/firebase';

const AdminContext = createContext();

export const useAdmin = () => useContext(AdminContext);

export const AdminProvider = ({ children }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Products CRUD
  const fetchProducts = async () => {
    try {
      setLoading(true);
      const q = query(collection(db, "products"), orderBy("name"));
      const querySnapshot = await getDocs(q);
      const productsData = [];

      querySnapshot.forEach((doc) => {
        productsData.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return productsData;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const createProduct = async (productData) => {
    try {
      setLoading(true);
      const docRef = await addDoc(collection(db, "products"), productData);
      return { id: docRef.id, ...productData };
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateProduct = async (productId, productData) => {
    // Implementasi update product
  };

  const deleteProduct = async (productId) => {
    // Implementasi delete product
  };

  // Orders management
  const updateOrderStatus = async (orderId, status) => {
    // Implementasi update order status
  };

  // Users management
  const fetchUsers = async () => {
    // Implementasi fetch users
  };

  const value = {
    loading,
    error,
    fetchProducts,
    createProduct,
    updateProduct,
    deleteProduct,
    updateOrderStatus,
    fetchUsers
  };

  return <AdminContext.Provider value={value}>{children}</AdminContext.Provider>;
};
