import { createContext, useState, useContext } from 'react';
import { collection, addDoc, getDocs, query, orderBy, doc, updateDoc, deleteDoc } from 'firebase/firestore';
import { db } from '../Config/firebase';

const AdminContext = createContext();

export const useAdmin = () => useContext(AdminContext);

export const AdminProvider = ({ children }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [products, setProducts] = useState([]);
  const [orders, setOrders] = useState([]);
  const [users, setUsers] = useState([]);

  // Products CRUD
  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      const q = query(collection(db, "products"), orderBy("name"));
      const querySnapshot = await getDocs(q);
      const productsData = [];

      querySnapshot.forEach((doc) => {
        productsData.push({
          id: doc.id,
          ...doc.data()
        });
      });

      setProducts(productsData);
      return productsData;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const createProduct = async (productData) => {
    try {
      setLoading(true);
      setError(null);
      const docRef = await addDoc(collection(db, "products"), {
        ...productData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      const newProduct = { id: docRef.id, ...productData };
      setProducts(prev => [...prev, newProduct]);
      return newProduct;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateProduct = async (productId, productData) => {
    try {
      setLoading(true);
      setError(null);
      const productRef = doc(db, "products", productId);
      await updateDoc(productRef, {
        ...productData,
        updatedAt: new Date()
      });
      const updatedProduct = { id: productId, ...productData };
      setProducts(prev => prev.map(product =>
        product.id === productId ? updatedProduct : product
      ));
      return updatedProduct;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteProduct = async (productId) => {
    try {
      setLoading(true);
      setError(null);
      await deleteDoc(doc(db, "products", productId));
      setProducts(prev => prev.filter(product => product.id !== productId));
      return true;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Orders management
  const fetchOrders = async () => {
    try {
      setLoading(true);
      setError(null);
      const q = query(collection(db, "orders"), orderBy("createdAt", "desc"));
      const querySnapshot = await getDocs(q);
      const ordersData = [];

      querySnapshot.forEach((doc) => {
        ordersData.push({
          id: doc.id,
          ...doc.data()
        });
      });

      setOrders(ordersData);
      return ordersData;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (orderId, status) => {
    try {
      setLoading(true);
      setError(null);
      const orderRef = doc(db, "orders", orderId);
      await updateDoc(orderRef, {
        status,
        updatedAt: new Date()
      });
      setOrders(prev => prev.map(order =>
        order.id === orderId ? { ...order, status, updatedAt: new Date() } : order
      ));
      return true;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Users management
  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      const q = query(collection(db, "users"), orderBy("email"));
      const querySnapshot = await getDocs(q);
      const usersData = [];

      querySnapshot.forEach((doc) => {
        usersData.push({
          id: doc.id,
          ...doc.data()
        });
      });

      setUsers(usersData);
      return usersData;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    loading,
    error,
    products,
    orders,
    users,
    fetchProducts,
    createProduct,
    updateProduct,
    deleteProduct,
    fetchOrders,
    updateOrderStatus,
    fetchUsers
  };

  return <AdminContext.Provider value={value}>{children}</AdminContext.Provider>;
};
