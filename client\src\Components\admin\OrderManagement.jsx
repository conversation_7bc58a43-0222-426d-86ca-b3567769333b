import React, { useState, useEffect } from 'react';
import { useAdmin } from '../../Context/AdminContext';

const OrderManagement = () => {
  const { 
    loading, 
    error, 
    orders, 
    fetchOrders, 
    updateOrderStatus 
  } = useAdmin();

  const [filterStatus, setFilterStatus] = useState('all');

  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);

  const ORDER_STATUSES = {
    ordered: { label: 'Ordered', color: 'bg-blue-100 text-blue-800' },
    paid: { label: 'Paid', color: 'bg-green-100 text-green-800' },
    processing: { label: 'Processing', color: 'bg-yellow-100 text-yellow-800' },
    ready: { label: 'Ready', color: 'bg-purple-100 text-purple-800' },
    delivering: { label: 'Delivering', color: 'bg-indigo-100 text-indigo-800' },
    completed: { label: 'Completed', color: 'bg-green-100 text-green-800' }
  };

  const handleStatusUpdate = async (orderId, newStatus) => {
    try {
      await updateOrderStatus(orderId, newStatus);
      fetchOrders(); // Refresh orders
    } catch (error) {
      console.error('Error updating order status:', error);
    }
  };

  const filteredOrders = filterStatus === 'all' 
    ? orders 
    : orders.filter(order => order.status === filterStatus);

  const getStatusOptions = (currentStatus) => {
    const statusFlow = ['ordered', 'paid', 'processing', 'ready', 'delivering', 'completed'];
    const currentIndex = statusFlow.indexOf(currentStatus);
    return statusFlow.slice(currentIndex);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Order Management</h1>
          <p className="text-gray-600">Manage customer orders and track status</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
          >
            <option value="all">All Orders</option>
            {Object.entries(ORDER_STATUSES).map(([status, config]) => (
              <option key={status} value={status}>{config.label}</option>
            ))}
          </select>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">Error: {error}</p>
        </div>
      )}

      <div className="grid gap-6">
        {filteredOrders.map((order) => (
          <div key={order.id} className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  Order #{order.orderNumber || order.id.substring(0, 8)}
                </h3>
                <p className="text-sm text-gray-600">
                  {order.createdAt?.toDate?.()?.toLocaleDateString() || 'Unknown date'}
                </p>
              </div>
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                ORDER_STATUSES[order.status]?.color || 'bg-gray-100 text-gray-800'
              }`}>
                {ORDER_STATUSES[order.status]?.label || order.status}
              </span>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Customer Information</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <p><span className="font-medium">Name:</span> {order.customerInfo?.name || 'N/A'}</p>
                  <p><span className="font-medium">Phone:</span> {order.customerInfo?.nomorHp || 'N/A'}</p>
                  <p><span className="font-medium">Address:</span> {order.customerInfo?.alamat || 'N/A'}</p>
                  <p><span className="font-medium">Payment:</span> {order.paymentMethod || 'N/A'}</p>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Order Items</h4>
                <div className="space-y-2">
                  {order.items?.map((item, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span>{item.name} x{item.quantity}</span>
                      <span>Rp {(item.price * item.quantity).toLocaleString()}</span>
                    </div>
                  )) || <p className="text-sm text-gray-500">No items found</p>}
                  <div className="border-t pt-2 mt-2">
                    <div className="flex justify-between font-medium">
                      <span>Total:</span>
                      <span>Rp {(order.total || 0).toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 pt-4 border-t">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Update Status:</span>
                <div className="flex space-x-2">
                  {getStatusOptions(order.status).map((status) => (
                    <button
                      key={status}
                      onClick={() => handleStatusUpdate(order.id, status)}
                      disabled={status === order.status}
                      className={`px-3 py-1 text-xs font-medium rounded-full transition-colors duration-200 ${
                        status === order.status
                          ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                          : 'bg-amber-100 text-amber-800 hover:bg-amber-200'
                      }`}
                    >
                      {ORDER_STATUSES[status]?.label || status}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}

        {filteredOrders.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📦</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
            <p className="text-gray-600">
              {filterStatus === 'all' 
                ? 'No orders have been placed yet.' 
                : `No orders with status "${ORDER_STATUSES[filterStatus]?.label || filterStatus}" found.`
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderManagement;
