import React, { useContext } from 'react';
import { OrderContext } from '../Context/OrderContext';

const OrderStatus = ({ currentStatus }) => {
  const { ORDER_STATUSES, getStatusInfo } = useContext(OrderContext);

  const statusOrder = [
    ORDER_STATUSES.ORDERED,
    ORDER_STATUSES.PAID,
    ORDER_STATUSES.PROCESSING,
    ORDER_STATUSES.READY,
    ORDER_STATUSES.DELIVERING,
    ORDER_STATUSES.COMPLETED
  ];

  const currentIndex = statusOrder.indexOf(currentStatus);

  const getStepIcon = (status, index) => {
    const isCompleted = index <= currentIndex;
    const isCurrent = index === currentIndex;

    const icons = {
      [ORDER_STATUSES.ORDERED]: '📋',
      [ORDER_STATUSES.PAID]: '💳',
      [ORDER_STATUSES.PROCESSING]: '👨‍🍳',
      [ORDER_STATUSES.READY]: '✅',
      [ORDER_STATUSES.DELIVERING]: '🚗',
      [ORDER_STATUSES.COMPLETED]: '🎉'
    };

    return (
      <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
        isCompleted
          ? 'bg-green-500 border-green-500 text-white'
          : isCurrent
          ? 'bg-blue-500 border-blue-500 text-white animate-pulse'
          : 'bg-gray-200 border-gray-300 text-gray-500'
      }`}>
        <span className="text-lg">{icons[status]}</span>
      </div>
    );
  };

  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-white to-emerald-50/30 border border-emerald-200/50 rounded-2xl shadow-lg backdrop-blur-sm">
      <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-green-500/5"></div>
      <div className="relative p-8">
        <div className="flex items-center gap-3 mb-8">
          <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-500 rounded-xl flex items-center justify-center">
            <span className="text-white text-lg">📊</span>
          </div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-emerald-700 to-green-700 bg-clip-text text-transparent">
            Status Pesanan
          </h2>
        </div>

        <div className="relative">
          {/* Progress Line */}
          <div className="absolute left-8 top-8 h-full w-1 bg-gradient-to-b from-gray-200 to-gray-100 rounded-full">
            <div
              className="bg-gradient-to-b from-emerald-500 to-green-500 w-full rounded-full transition-all duration-1000 ease-out shadow-sm"
              style={{ height: `${(currentIndex / (statusOrder.length - 1)) * 100}%` }}
            />
          </div>

          {/* Status Steps */}
          <div className="space-y-6">
            {statusOrder.map((status, index) => {
              const statusInfo = getStatusInfo(status);
              const isCompleted = index <= currentIndex;
              const isCurrent = index === currentIndex;

              return (
                <div key={status} className="relative flex items-start group">
                  {/* Icon */}
                  <div className="relative z-10">
                    {getStepIcon(status, index)}
                  </div>

                  {/* Content */}
                  <div className="ml-6 flex-1">
                    <div className={`font-semibold text-lg transition-colors duration-300 ${
                      isCompleted ? 'text-emerald-600' :
                      isCurrent ? 'text-blue-600' : 'text-gray-400'
                    }`}>
                      {statusInfo.label}
                    </div>
                    <div className={`text-sm mt-1 transition-colors duration-300 ${
                      isCompleted || isCurrent ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      {statusInfo.description}
                    </div>
                    {isCurrent && (
                      <div className="mt-3 animate-pulse">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-ping"></div>
                          Sedang Berlangsung
                        </span>
                      </div>
                    )}
                    {isCompleted && !isCurrent && (
                      <div className="mt-3">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 border border-emerald-200">
                          <div className="w-2 h-2 bg-emerald-500 rounded-full mr-2"></div>
                          Selesai
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Timestamp (if completed) */}
                  {isCompleted && (
                    <div className="text-xs text-gray-500 bg-white/60 backdrop-blur-sm px-2 py-1 rounded-md border border-gray-200">
                      {new Date().toLocaleTimeString('id-ID', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Current Status Highlight Card */}
        <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 rounded-2xl backdrop-blur-sm">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white text-xl">⚡</span>
            </div>
            <div className="flex-1">
              <div className="font-bold text-gray-800 text-lg">
                Status Saat Ini: {getStatusInfo(currentStatus).label}
              </div>
              <div className="text-sm text-gray-600 mt-1">
                {getStatusInfo(currentStatus).description}
              </div>
            </div>
            <div className="hidden sm:block">
              <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full animate-pulse shadow-lg"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderStatus;
