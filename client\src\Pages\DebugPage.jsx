import { useUser } from "../Context/UserContext";
import { auth } from "../Config/firebase";
import { makeUserAdmin } from "../Utils/Admin/createAdmin";
import { useNavigate } from "react-router-dom";

const DebugPage = () => {
  const { user, userRole, loading } = useUser();
  const navigate = useNavigate();

  const handleMakeAdmin = async () => {
    try {
      const currentUser = auth.currentUser;
      if (currentUser) {
        await makeUserAdmin(currentUser.uid, currentUser.email);
        alert('You have been made admin! Please refresh the page.');
        window.location.reload();
      } else {
        alert('Please login first');
      }
    } catch (error) {
      console.error('Error making user admin:', error);
      alert('Error making user admin. Check console for details.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Debug Information</h1>
          
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-2">User Context Status:</h3>
              <div className="space-y-2 text-sm">
                <p><span className="font-medium">Loading:</span> {loading ? 'Yes' : 'No'}</p>
                <p><span className="font-medium">User:</span> {user ? user.email : 'Not logged in'}</p>
                <p><span className="font-medium">User Role:</span> {userRole || 'None'}</p>
                <p><span className="font-medium">Is Admin:</span> {userRole === 'admin' ? 'Yes' : 'No'}</p>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-2">Firebase Auth Status:</h3>
              <div className="space-y-2 text-sm">
                <p><span className="font-medium">Current User:</span> {auth.currentUser ? auth.currentUser.email : 'Not logged in'}</p>
                <p><span className="font-medium">UID:</span> {auth.currentUser ? auth.currentUser.uid : 'None'}</p>
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Quick Actions:</h3>
              <div className="space-y-2">
                <button
                  onClick={handleMakeAdmin}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
                >
                  Make Current User Admin
                </button>
                
                <button
                  onClick={() => window.location.reload()}
                  className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700"
                >
                  Refresh Page
                </button>

                <button
                  onClick={() => navigate('/admin')}
                  className="w-full bg-amber-600 text-white py-2 px-4 rounded-md hover:bg-amber-700"
                >
                  Try Access Admin Panel
                </button>

                <button
                  onClick={() => navigate('/')}
                  className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700"
                >
                  Go to Home
                </button>
              </div>
            </div>

            <div className="bg-yellow-50 p-4 rounded-lg">
              <h3 className="font-semibold text-yellow-900 mb-2">Troubleshooting Steps:</h3>
              <ol className="list-decimal list-inside text-sm text-yellow-800 space-y-1">
                <li>Make sure you're logged in</li>
                <li>Click "Make Current User Admin" button</li>
                <li>Refresh the page</li>
                <li>Check if role changed to "admin"</li>
                <li>Try accessing admin panel</li>
              </ol>
            </div>

            <div className="bg-red-50 p-4 rounded-lg">
              <h3 className="font-semibold text-red-900 mb-2">Console Logs:</h3>
              <p className="text-sm text-red-800">
                Open browser console (F12) to see detailed logs about user authentication and role checking.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugPage;
