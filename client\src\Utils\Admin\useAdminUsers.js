import { useState } from 'react';
import { collection, getDocs, doc, updateDoc, query, orderBy } from 'firebase/firestore';
import { db } from '../../Config/firebase';

const useAdminUsers = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      const q = query(collection(db, "users"), orderBy("email"));
      const querySnapshot = await getDocs(q);
      const usersData = [];

      querySnapshot.forEach((doc) => {
        usersData.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return usersData;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateUserRole = async (userId, role) => {
    try {
      setLoading(true);
      setError(null);
      const userRef = doc(db, "users", userId);
      await updateDoc(userRef, {
        role,
        updatedAt: new Date()
      });
      return { id: userId, role, updatedAt: new Date() };
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const getUserStats = (users) => {
    const stats = {
      total: users.length,
      admin: 0,
      user: 0,
      active: 0,
      inactive: 0
    };

    users.forEach(user => {
      if (user.role === 'admin') stats.admin++;
      else stats.user++;
      
      if (user.status === 'active') stats.active++;
      else stats.inactive++;
    });

    return stats;
  };

  return {
    loading,
    error,
    fetchUsers,
    updateUserRole,
    getUserStats
  };
};

export default useAdminUsers;
