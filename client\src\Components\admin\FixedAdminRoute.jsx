import { useState, useEffect } from 'react';
import { auth } from '../../Config/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../../Config/firebase';

const FixedAdminRoute = ({ children }) => {
  const [authState, setAuthState] = useState({
    loading: true,
    user: null,
    isAdmin: false,
    checked: false
  });

  useEffect(() => {
    let mounted = true;

    const checkAuth = async () => {
      try {
        // Wait for auth to be ready
        await new Promise((resolve) => {
          const unsubscribe = auth.onAuthStateChanged((user) => {
            unsubscribe();
            resolve(user);
          });
        });

        const user = auth.currentUser;
        console.log('FixedAdminRoute: Current user:', user?.email);

        if (!user) {
          if (mounted) {
            setAuthState({
              loading: false,
              user: null,
              isAdmin: false,
              checked: true
            });
          }
          return;
        }

        // Check user role
        const userDoc = await getDoc(doc(db, 'users', user.uid));
        const isAdmin = userDoc.exists() && userDoc.data().role === 'admin';
        
        console.log('FixedAdminRoute: User role check:', {
          exists: userDoc.exists(),
          role: userDoc.data()?.role,
          isAdmin
        });

        if (mounted) {
          setAuthState({
            loading: false,
            user,
            isAdmin,
            checked: true
          });
        }
      } catch (error) {
        console.error('FixedAdminRoute: Error checking auth:', error);
        if (mounted) {
          setAuthState({
            loading: false,
            user: null,
            isAdmin: false,
            checked: true
          });
        }
      }
    };

    checkAuth();

    return () => {
      mounted = false;
    };
  }, []);

  if (!authState.checked || authState.loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Checking access...</p>
        </div>
      </div>
    );
  }

  if (!authState.user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🔐</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Please Login</h1>
          <p className="text-gray-600 mb-4">You need to login to access this area.</p>
          <a
            href="/login"
            className="bg-amber-600 text-white px-6 py-2 rounded-md hover:bg-amber-700"
          >
            Go to Login
          </a>
        </div>
      </div>
    );
  }

  if (!authState.isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🚫</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600 mb-4">You don't have admin privileges.</p>
          <div className="space-y-2">
            <p className="text-sm text-gray-500">User: {authState.user.email}</p>
            <div className="space-x-4">
              <a
                href="/"
                className="bg-gray-600 text-white px-6 py-2 rounded-md hover:bg-gray-700"
              >
                Go Home
              </a>
              <a
                href="/make-admin"
                className="bg-amber-600 text-white px-6 py-2 rounded-md hover:bg-amber-700"
              >
                Become Admin
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return children;
};

export default FixedAdminRoute;
