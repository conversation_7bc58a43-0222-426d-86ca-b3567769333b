import { collection, addDoc } from 'firebase/firestore';
import { db } from '../../Config/firebase';

const sampleProducts = [
  {
    name: 'Espresso Classic',
    description: 'Kopi espresso klasik dengan rasa yang kuat dan aroma yang menggugah selera',
    price: 25000,
    imageUrl: 'https://images.unsplash.com/photo-1510707577719-ae7c14805e3a?w=400',
    category: 'coffee',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Cappuccino Deluxe',
    description: 'Cappuccino premium dengan foam susu yang lembut dan taburan cokelat',
    price: 35000,
    imageUrl: 'https://images.unsplash.com/photo-1572442388796-11668a67e53d?w=400',
    category: 'coffee',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Latte Art Special',
    description: 'Latte dengan seni latte yang indah, perpaduan sempurna kopi dan susu',
    price: 40000,
    imageUrl: 'https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=400',
    category: 'coffee',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Green Tea Latte',
    description: 'Teh hijau premium dengan susu steamed, menyegarkan dan sehat',
    price: 30000,
    imageUrl: 'https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=400',
    category: 'tea',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Croissant Butter',
    description: 'Croissant segar dengan mentega premium, sempurna untuk sarapan',
    price: 20000,
    imageUrl: 'https://images.unsplash.com/photo-1555507036-ab794f4ade2a?w=400',
    category: 'pastry',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Chocolate Muffin',
    description: 'Muffin cokelat lembut dengan choco chips, manis dan menggugah selera',
    price: 18000,
    imageUrl: 'https://images.unsplash.com/photo-*************-41aef7caefaa?w=400',
    category: 'pastry',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const sampleOrders = [
  {
    orderNumber: 'ORD-001',
    customerInfo: {
      name: 'John Doe',
      alamat: 'Jl. Sudirman No. 123, Jakarta',
      nomorHp: '************'
    },
    items: [
      { name: 'Espresso Classic', price: 25000, quantity: 2 },
      { name: 'Croissant Butter', price: 20000, quantity: 1 }
    ],
    total: 70000,
    paymentMethod: 'Transfer Bank',
    status: 'processing',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    orderNumber: 'ORD-002',
    customerInfo: {
      name: 'Jane Smith',
      alamat: 'Jl. Thamrin No. 456, Jakarta',
      nomorHp: '************'
    },
    items: [
      { name: 'Cappuccino Deluxe', price: 35000, quantity: 1 },
      { name: 'Chocolate Muffin', price: 18000, quantity: 2 }
    ],
    total: 71000,
    paymentMethod: 'E-Wallet',
    status: 'completed',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

export const seedProducts = async () => {
  try {
    console.log('Seeding products...');
    for (const product of sampleProducts) {
      await addDoc(collection(db, 'products'), product);
    }
    console.log('Products seeded successfully!');
  } catch (error) {
    console.error('Error seeding products:', error);
  }
};

export const seedOrders = async () => {
  try {
    console.log('Seeding orders...');
    for (const order of sampleOrders) {
      await addDoc(collection(db, 'orders'), order);
    }
    console.log('Orders seeded successfully!');
  } catch (error) {
    console.error('Error seeding orders:', error);
  }
};

export const seedAllData = async () => {
  await seedProducts();
  await seedOrders();
  console.log('All sample data seeded successfully!');
};
