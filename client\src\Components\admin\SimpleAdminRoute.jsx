import { useUser } from '../../Context/UserContext';

const SimpleAdminRoute = ({ children }) => {
  const { user, userRole, loading } = useUser();

  console.log('SimpleAdminRoute - User:', user?.email, 'Role:', userRole, 'Loading:', loading);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🔐</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Please Login</h1>
          <p className="text-gray-600 mb-4">You need to login to access this area.</p>
          <a
            href="/login"
            className="bg-amber-600 text-white px-6 py-2 rounded-md hover:bg-amber-700"
          >
            Go to Login
          </a>
        </div>
      </div>
    );
  }

  if (userRole !== 'admin') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🚫</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600 mb-4">You don't have admin privileges.</p>
          <div className="space-y-2">
            <p className="text-sm text-gray-500">Current role: {userRole}</p>
            <div className="space-x-4">
              <a
                href="/"
                className="bg-gray-600 text-white px-6 py-2 rounded-md hover:bg-gray-700"
              >
                Go Home
              </a>
              <a
                href="/make-admin"
                className="bg-amber-600 text-white px-6 py-2 rounded-md hover:bg-amber-700"
              >
                Become Admin
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return children;
};

export default SimpleAdminRoute;
