import { doc, setDoc, getDoc } from 'firebase/firestore';
import { db } from '../../Config/firebase';

// Function to make a user admin
export const makeUserAdmin = async (userId, email) => {
  try {
    const userRef = doc(db, 'users', userId);
    
    // Check if user document exists
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      // Update existing user to admin
      await setDoc(userRef, {
        ...userDoc.data(),
        role: 'admin',
        updatedAt: new Date()
      }, { merge: true });
    } else {
      // Create new admin user document
      await setDoc(userRef, {
        email: email,
        role: 'admin',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
    
    console.log(`User ${email} has been made admin successfully!`);
    return true;
  } catch (error) {
    console.error('Error making user admin:', error);
    throw error;
  }
};

// Function to create admin user manually (for testing)
export const createTestAdmin = async () => {
  try {
    // You can change this email to your test admin email
    const adminEmail = '<EMAIL>';
    const adminId = 'test-admin-id'; // In real app, this would be the Firebase Auth UID
    
    await setDoc(doc(db, 'users', adminId), {
      email: adminEmail,
      role: 'admin',
      status: 'active',
      name: 'Test Admin',
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    console.log('Test admin user created successfully!');
    return true;
  } catch (error) {
    console.error('Error creating test admin:', error);
    throw error;
  }
};

// Instructions for making yourself admin:
/*
1. Register/Login with your email
2. Go to Firebase Console > Firestore Database
3. Find your user document in the 'users' collection
4. Edit the document and change 'role' field from 'user' to 'admin'
5. Save the changes
6. Refresh your app and you should see the Admin link

OR

Use this function in browser console after logging in:
```javascript
import { makeUserAdmin } from './Utils/Admin/createAdmin.js';
import { auth } from './Config/firebase.js';

// Make current user admin
makeUserAdmin(auth.currentUser.uid, auth.currentUser.email);
```
*/
