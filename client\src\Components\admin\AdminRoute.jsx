import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { auth } from '../../Config/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../../Config/firebase';

const AdminRoute = ({ children }) => {
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [authChecked, setAuthChecked] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    let isMounted = true;

    const checkAdminAccess = async (user) => {
      try {
        console.log('Checking admin access for user:', user?.email);

        if (!user) {
          console.log('No user found, redirecting to login');
          if (isMounted) {
            setLoading(false);
            navigate('/login');
          }
          return;
        }

        // Check if user has admin role in Firestore
        const userDoc = await getDoc(doc(db, 'users', user.uid));
        console.log('User document exists:', userDoc.exists());

        if (userDoc.exists()) {
          const userData = userDoc.data();
          console.log('User data:', userData);

          if (userData.role === 'admin') {
            console.log('User is admin, granting access');
            if (isMounted) {
              setIsAdmin(true);
              setLoading(false);
            }
          } else {
            console.log('User is not admin, redirecting to home');
            if (isMounted) {
              setLoading(false);
              navigate('/');
            }
          }
        } else {
          console.log('User document does not exist, redirecting to home');
          if (isMounted) {
            setLoading(false);
            navigate('/');
          }
        }
      } catch (error) {
        console.error('Error checking admin access:', error);
        if (isMounted) {
          setLoading(false);
          navigate('/');
        }
      }
    };

    // Listen for auth state changes
    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (!authChecked) {
        setAuthChecked(true);
        checkAdminAccess(user);
      }
    });

    return () => {
      isMounted = false;
      unsubscribe();
    };
  }, [navigate, authChecked]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Checking admin access...</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🚫</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600 mb-4">You don't have permission to access this area.</p>
          <button
            onClick={() => navigate('/')}
            className="bg-amber-600 text-white px-6 py-2 rounded-md hover:bg-amber-700"
          >
            Go Home
          </button>
        </div>
      </div>
    );
  }

  return children;
};

export default AdminRoute;
