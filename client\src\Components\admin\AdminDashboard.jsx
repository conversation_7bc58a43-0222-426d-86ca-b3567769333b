import React, { useState, useEffect } from 'react';
import { useAdmin } from '../../Context/AdminContext';
import { seedAllData } from '../../Utils/Admin/seedData';

const AdminDashboard = () => {
  const {
    loading,
    error,
    products,
    orders,
    users,
    fetchProducts,
    fetchOrders,
    fetchUsers
  } = useAdmin();

  const [stats, setStats] = useState({
    totalProducts: 0,
    totalOrders: 0,
    totalUsers: 0,
    pendingOrders: 0,
    completedOrders: 0,
    totalRevenue: 0
  });

  const handleSeedData = async () => {
    try {
      await seedAllData();
      // Refresh data after seeding
      await Promise.all([
        fetchProducts(),
        fetchOrders(),
        fetchUsers()
      ]);
      alert('Sample data seeded successfully!');
    } catch (error) {
      console.error('Error seeding data:', error);
      alert('Error seeding data. Check console for details.');
    }
  };

  useEffect(() => {
    const loadData = async () => {
      try {
        await Promise.all([
          fetchProducts(),
          fetchOrders(),
          fetchUsers()
        ]);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      }
    };

    loadData();
  }, [fetchProducts, fetchOrders, fetchUsers]);

  useEffect(() => {
    // Calculate stats when data changes
    const pendingOrders = orders.filter(order =>
      ['ordered', 'paid', 'processing'].includes(order.status)
    ).length;

    const completedOrders = orders.filter(order =>
      order.status === 'completed'
    ).length;

    const totalRevenue = orders
      .filter(order => order.status === 'completed')
      .reduce((sum, order) => sum + (order.total || 0), 0);

    setStats({
      totalProducts: products.length,
      totalOrders: orders.length,
      totalUsers: users.length,
      pendingOrders,
      completedOrders,
      totalRevenue
    });
  }, [products, orders, users]);

  const StatCard = ({ title, value, icon, color = 'blue' }) => (
    <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-l-blue-500">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
        <div className={`p-3 rounded-full bg-${color}-100`}>
          <span className="text-2xl">{icon}</span>
        </div>
      </div>
    </div>
  );

  const RecentOrders = () => {
    const recentOrders = orders.slice(0, 5);

    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Orders</h3>
        <div className="space-y-3">
          {recentOrders.map((order) => (
            <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="font-medium text-gray-900">
                  {order.orderNumber || order.id}
                </p>
                <p className="text-sm text-gray-600">
                  {order.customerInfo?.name || 'Unknown Customer'}
                </p>
              </div>
              <div className="text-right">
                <p className="font-medium text-gray-900">
                  Rp {(order.total || 0).toLocaleString()}
                </p>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  order.status === 'completed' ? 'bg-green-100 text-green-800' :
                  order.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  {order.status}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-800">Error loading dashboard: {error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome to Coffee Right Admin Panel</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <StatCard
          title="Total Products"
          value={stats.totalProducts}
          icon="☕"
          color="amber"
        />
        <StatCard
          title="Total Orders"
          value={stats.totalOrders}
          icon="📦"
          color="blue"
        />
        <StatCard
          title="Total Users"
          value={stats.totalUsers}
          icon="👥"
          color="green"
        />
        <StatCard
          title="Pending Orders"
          value={stats.pendingOrders}
          icon="⏳"
          color="yellow"
        />
        <StatCard
          title="Completed Orders"
          value={stats.completedOrders}
          icon="✅"
          color="green"
        />
        <StatCard
          title="Total Revenue"
          value={`Rp ${stats.totalRevenue.toLocaleString()}`}
          icon="💰"
          color="emerald"
        />
      </div>

      {/* Recent Orders */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RecentOrders />

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full text-left p-3 bg-amber-50 hover:bg-amber-100 rounded-lg transition-colors duration-200">
              <span className="text-amber-600 font-medium">Add New Product</span>
            </button>
            <button className="w-full text-left p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200">
              <span className="text-blue-600 font-medium">View All Orders</span>
            </button>
            <button className="w-full text-left p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors duration-200">
              <span className="text-green-600 font-medium">Manage Users</span>
            </button>
            <button
              onClick={handleSeedData}
              className="w-full text-left p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors duration-200"
            >
              <span className="text-purple-600 font-medium">Seed Sample Data</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
