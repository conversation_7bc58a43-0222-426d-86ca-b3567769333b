import React, { useContext } from 'react';
import { OrderContext } from '../Context/OrderContext';

const AdminOrderPanel = ({ orderId, currentStatus }) => {
  const { updateOrderStatus, ORDER_STATUSES, getStatusInfo } = useContext(OrderContext);

  const statusOrder = [
    ORDER_STATUSES.ORDERED,
    ORDER_STATUSES.PAID,
    ORDER_STATUSES.PROCESSING,
    ORDER_STATUSES.READY,
    ORDER_STATUSES.DELIVERING,
    ORDER_STATUSES.COMPLETED
  ];

  const handleStatusUpdate = async (newStatus) => {
    try {
      await updateOrderStatus(orderId, newStatus);
      alert(`Status berhasil diperbarui ke: ${getStatusInfo(newStatus).label}`);
    } catch (error) {
      alert('Gagal memperbarui status');
      console.error('Error updating status:', error);
    }
  };

  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200/50 rounded-2xl shadow-lg backdrop-blur-sm mt-6">
      <div className="absolute inset-0 bg-gradient-to-br from-slate-500/5 to-gray-500/5"></div>
      <div className="relative p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-gradient-to-br from-slate-500 to-gray-500 rounded-xl flex items-center justify-center">
            <span className="text-white text-lg">🔧</span>
          </div>
          <div>
            <h3 className="text-xl font-bold bg-gradient-to-r from-slate-700 to-gray-700 bg-clip-text text-transparent">
              Panel Admin (Demo)
            </h3>
            <p className="text-sm text-gray-600">
              Simulasi perubahan status pesanan
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {statusOrder.map((status) => {
            const statusInfo = getStatusInfo(status);
            const isCurrentStatus = status === currentStatus;

            return (
              <button
                key={status}
                onClick={() => handleStatusUpdate(status)}
                disabled={isCurrentStatus}
                className={`group relative p-4 rounded-xl font-medium transition-all duration-300 transform ${
                  isCurrentStatus
                    ? 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 cursor-not-allowed border-2 border-blue-200 shadow-inner'
                    : 'bg-white/70 hover:bg-white text-gray-700 border border-gray-200 hover:border-gray-300 hover:shadow-md hover:scale-[1.02] active:scale-[0.98]'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                    isCurrentStatus
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 text-gray-600 group-hover:bg-gray-200'
                  }`}>
                    <span className="text-sm">
                      {status === 'ordered' && '📋'}
                      {status === 'paid' && '💳'}
                      {status === 'processing' && '👨‍🍳'}
                      {status === 'ready' && '✅'}
                      {status === 'delivering' && '🚗'}
                      {status === 'completed' && '🎉'}
                    </span>
                  </div>
                  <div className="text-left flex-1">
                    <div className="font-semibold text-sm">
                      {statusInfo.label}
                    </div>
                    {isCurrentStatus && (
                      <div className="text-xs text-blue-600 mt-1">
                        Status Aktif
                      </div>
                    )}
                  </div>
                </div>
                {!isCurrentStatus && (
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-gray-400/10 to-slate-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                )}
              </button>
            );
          })}
        </div>

        <div className="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-xl">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-amber-500">⚠️</span>
            <span className="font-semibold text-amber-800 text-sm">Catatan Demo</span>
          </div>
          <p className="text-xs text-amber-700">
            Panel ini hanya untuk demonstrasi. Dalam aplikasi nyata, status akan diperbarui otomatis oleh sistem backend berdasarkan proses bisnis yang sebenarnya.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AdminOrderPanel;
