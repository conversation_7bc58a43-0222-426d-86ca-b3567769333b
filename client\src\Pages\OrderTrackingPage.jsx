import React, { useContext, useEffect, useState } from 'react';
import { useParams, Link } from 'react-router-dom';
import { OrderContext } from '../Context/OrderContext';
import OrderStatus from '../Components/OrderStatus';
import OrderDetails from '../Components/OrderDetails';
import AdminOrderPanel from '../Components/AdminOrderPanel';

const OrderTrackingPage = () => {
  const { orderId } = useParams();
  const { getOrderById, ORDER_STATUSES } = useContext(OrderContext);
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchOrder = () => {
      const foundOrder = getOrderById(orderId);
      setOrder(foundOrder);
      setLoading(false);
    };

    fetchOrder();
  }, [orderId, getOrderById]);



  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Memuat detail pesanan...</p>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-100 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😕</div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Pesanan Tidak Ditemukan</h1>
          <p className="text-gray-600 mb-6">Pesanan dengan ID tersebut tidak dapat ditemukan.</p>
          <Link
            to="/products"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition-colors duration-200"
          >
            Kembali Berbelanja
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-100 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-amber-500 to-orange-500 rounded-2xl mb-6 shadow-lg">
            <span className="text-white text-2xl">📦</span>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-amber-700 to-orange-700 bg-clip-text text-transparent mb-3">
            Lacak Pesanan
          </h1>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Pantau status pesanan Anda secara real-time dan dapatkan update terbaru tentang proses pengiriman
          </p>
        </div>

        {/* Navigation */}
        <div className="mb-8">
          <Link
            to="/products"
            className="group inline-flex items-center gap-2 px-4 py-2 bg-white/70 backdrop-blur-sm border border-white/20 rounded-xl hover:bg-white/90 transition-all duration-200 hover:shadow-md"
          >
            <svg className="w-4 h-4 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span className="font-medium text-gray-700">Kembali ke Produk</span>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

          <div>
            <OrderStatus currentStatus={order.status} />


            <AdminOrderPanel orderId={orderId} currentStatus={order.status} />



          </div>


          <div>
            <OrderDetails order={order} />
          </div>
        </div>

        {/* Success Message for Completed Orders */}
        {order.status === ORDER_STATUSES.COMPLETED && (
          <div className="mt-12 relative overflow-hidden bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200/50 rounded-2xl shadow-lg backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5"></div>
            <div className="relative p-8 text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <span className="text-white text-3xl">🎉</span>
              </div>
              <h2 className="text-3xl font-bold bg-gradient-to-r from-green-700 to-emerald-700 bg-clip-text text-transparent mb-4">
                Pesanan Selesai!
              </h2>
              <p className="text-green-700 text-lg mb-8 max-w-md mx-auto">
                Terima kasih telah berbelanja dengan kami. Pesanan Anda telah berhasil diantar dengan sempurna.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  to="/products"
                  className="group inline-flex items-center justify-center gap-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl"
                >
                  <span>🛍️</span>
                  <span>Pesan Lagi</span>
                  <span className="group-hover:translate-x-1 transition-transform duration-200">→</span>
                </Link>
                <button className="group inline-flex items-center justify-center gap-2 bg-white/70 hover:bg-white text-gray-800 px-8 py-3 rounded-xl font-semibold border border-gray-200 hover:border-gray-300 transition-all duration-300 transform hover:scale-[1.02] shadow-md hover:shadow-lg">
                  <span>⭐</span>
                  <span>Beri Rating</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderTrackingPage;
