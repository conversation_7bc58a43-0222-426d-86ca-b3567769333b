import React from 'react';

const OrderDetails = ({ order }) => {
  if (!order) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="text-center text-gray-500">
          <p>Order tidak ditemukan</p>
        </div>
      </div>
    );
  }

  const formatDate = (date) => {
    return new Date(date.seconds ? date.seconds * 1000 : date).toLocaleString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-8">
      {/* Order Header */}
      <div className="relative overflow-hidden bg-gradient-to-br from-white to-purple-50/30 border border-purple-200/50 rounded-2xl shadow-lg backdrop-blur-sm">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5"></div>
        <div className="relative p-8">
          <div className="flex justify-between items-start mb-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                <span className="text-white text-xl">📄</span>
              </div>
              <div>
                <h2 className="text-2xl font-bold bg-gradient-to-r from-purple-700 to-pink-700 bg-clip-text text-transparent">
                  Detail Pesanan
                </h2>
                <p className="text-gray-600 font-mono text-sm bg-gray-100 px-2 py-1 rounded-md mt-1">
                  #{order.orderNumber}
                </p>
              </div>
            </div>
            <div className="text-right bg-white/60 backdrop-blur-sm p-4 rounded-xl border border-white/20">
              <p className="text-sm font-semibold text-gray-500 uppercase tracking-wide">Tanggal Pesanan</p>
              <p className="font-bold text-gray-800 mt-1">{formatDate(order.createdAt)}</p>
            </div>
          </div>

          {/* Customer Info */}
          <div className="border-t border-purple-200 pt-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-400 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm">👤</span>
              </div>
              <h3 className="font-bold text-gray-800 text-lg">Informasi Pelanggan</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white/60 backdrop-blur-sm p-4 rounded-xl border border-white/20">
                <p className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-1">Nama</p>
                <p className="font-bold text-gray-800">{order.customerInfo.name}</p>
              </div>
              <div className="bg-white/60 backdrop-blur-sm p-4 rounded-xl border border-white/20">
                <p className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-1">Nomor HP</p>
                <p className="font-bold text-gray-800">{order.customerInfo.nomorHp}</p>
              </div>
              <div className="md:col-span-2 bg-white/60 backdrop-blur-sm p-4 rounded-xl border border-white/20">
                <p className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-1">Alamat</p>
                <p className="font-bold text-gray-800">{order.customerInfo.alamat}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Order Items */}
      <div className="relative overflow-hidden bg-gradient-to-br from-white to-orange-50/30 border border-orange-200/50 rounded-2xl shadow-lg backdrop-blur-sm">
        <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-amber-500/5"></div>
        <div className="relative p-8">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-amber-500 rounded-xl flex items-center justify-center">
              <span className="text-white text-lg">🛍️</span>
            </div>
            <h3 className="text-2xl font-bold bg-gradient-to-r from-orange-700 to-amber-700 bg-clip-text text-transparent">
              Item Pesanan
            </h3>
          </div>

          <div className="space-y-4">
            {order.items.map((item, index) => (
              <div key={item.id} className="group relative">
                <div className="flex items-center gap-4 p-6 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/80 transition-all duration-300 hover:shadow-md">
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-100 to-amber-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-lg font-bold text-orange-700">{index + 1}</span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-bold text-gray-900 text-lg">{item.name}</h4>
                    <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                    <div className="flex items-center gap-3 mt-2">
                      <span className="inline-flex items-center px-2 py-1 rounded-md bg-orange-100 text-orange-800 text-xs font-medium">
                        Qty: {item.quantity}
                      </span>
                      <span className="text-sm text-gray-500">
                        @ Rp {item.price.toLocaleString('id-ID')}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-gray-900 text-xl">
                      Rp {(item.price * item.quantity).toLocaleString('id-ID')}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Order Summary */}
          <div className="mt-8 pt-6 border-t border-orange-200">
            <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-6 border border-orange-200/50">
              <div className="space-y-3">
                <div className="flex justify-between text-gray-700">
                  <span className="font-medium">Subtotal</span>
                  <span className="font-semibold">Rp {order.total.toLocaleString('id-ID')}</span>
                </div>
                <div className="flex justify-between text-gray-700">
                  <span className="font-medium">Ongkos Kirim</span>
                  <span className="font-semibold text-green-600">Gratis</span>
                </div>
                <div className="border-t border-orange-200 pt-3">
                  <div className="flex justify-between items-center">
                    <span className="text-xl font-bold text-gray-800">Total Pembayaran</span>
                    <span className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent">
                      Rp {order.total.toLocaleString('id-ID')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Method */}
          <div className="mt-6 p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <span className="text-orange-500">💳</span>
                <span className="font-semibold text-gray-700">Metode Pembayaran</span>
              </div>
              <span className="font-bold text-gray-900 bg-orange-100 px-3 py-1 rounded-lg">
                {order.paymentMethod === 'cod' ? '🚚 Cash on Delivery (COD)' : '🏦 Transfer Bank'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* System Info */}
      <div className="relative overflow-hidden bg-gradient-to-r from-gray-50 to-slate-50 border border-gray-200/50 rounded-2xl shadow-sm">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-500/5 to-slate-500/5"></div>
        <div className="relative p-6 text-center">
          <div className="w-12 h-12 bg-gradient-to-br from-gray-400 to-slate-400 rounded-xl flex items-center justify-center mx-auto mb-3">
            <span className="text-white text-lg">⚙️</span>
          </div>
          <p className="text-sm font-medium text-gray-600">
            Status pesanan akan diperbarui secara otomatis oleh sistem
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Anda akan menerima notifikasi untuk setiap perubahan status
          </p>
        </div>
      </div>
    </div>
  );
};

export default OrderDetails;
