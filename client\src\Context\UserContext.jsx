import { createContext, useState, useContext, useEffect } from 'react';
import { auth } from '../Config/firebase';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db } from '../Config/firebase';

const UserContext = createContext();

export const useUser = () => useContext(UserContext);

export const UserProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let isMounted = true;

    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      try {
        if (firebaseUser && isMounted) {
          console.log('UserContext: User logged in:', firebaseUser.email);

          // Get user data from Firestore
          const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));

          if (userDoc.exists()) {
            const userData = userDoc.data();
            console.log('UserContext: User data found:', userData);
            if (isMounted) {
              setUser(firebaseUser);
              setUserRole(userData.role || 'user');
            }
          } else {
            console.log('UserContext: Creating new user document');
            // Create user document if it doesn't exist
            const newUserData = {
              email: firebaseUser.email,
              role: 'user', // Default role
              status: 'active',
              createdAt: new Date(),
              updatedAt: new Date()
            };

            await setDoc(doc(db, 'users', firebaseUser.uid), newUserData);
            if (isMounted) {
              setUser(firebaseUser);
              setUserRole('user');
            }
          }
        } else if (isMounted) {
          console.log('UserContext: No user logged in');
          setUser(null);
          setUserRole(null);
        }
      } catch (error) {
        console.error('UserContext: Error fetching user data:', error);
        if (isMounted) {
          setUser(firebaseUser);
          setUserRole('user'); // Default fallback
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    });

    return () => {
      isMounted = false;
      unsubscribe();
    };
  }, []);

  const isAdmin = () => userRole === 'admin';
  const isUser = () => userRole === 'user';

  const value = {
    user,
    userRole,
    loading,
    isAdmin,
    isUser
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};
