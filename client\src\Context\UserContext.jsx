import { createContext, useState, useContext, useEffect } from 'react';
import { auth } from '../Config/firebase';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db } from '../Config/firebase';

const UserContext = createContext();

export const useUser = () => useContext(UserContext);

export const UserProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          // Get user data from Firestore
          const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
          
          if (userDoc.exists()) {
            const userData = userDoc.data();
            setUser(firebaseUser);
            setUserRole(userData.role || 'user');
          } else {
            // Create user document if it doesn't exist
            const newUserData = {
              email: firebaseUser.email,
              role: 'user', // Default role
              status: 'active',
              createdAt: new Date(),
              updatedAt: new Date()
            };
            
            await setDoc(doc(db, 'users', firebaseUser.uid), newUserData);
            setUser(firebaseUser);
            setUserRole('user');
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
          setUser(firebaseUser);
          setUserRole('user'); // Default fallback
        }
      } else {
        setUser(null);
        setUserRole(null);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const isAdmin = () => userRole === 'admin';
  const isUser = () => userRole === 'user';

  const value = {
    user,
    userRole,
    loading,
    isAdmin,
    isUser
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};
