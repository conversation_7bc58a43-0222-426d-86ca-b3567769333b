import React, { useState, useContext } from "react";
import { CartContext } from "../Context/CartContext";
import { OrderContext } from "../Context/OrderContext";
import { useNavigate } from "react-router-dom";

const FormPembelian = () => {
    const [name, setName] = useState('')
    const [alamat, setAlamat] = useState('')
    const [nomorHp, setNomorHp] = useState('')
    const [paymentMethod, setPaymentMethod] = useState('cod')
    const [isSubmitting, setIsSubmitting] = useState(false)

    const { cartItems, getCartTotal, clearCart } = useContext(CartContext);
    const { createOrder } = useContext(OrderContext);
    const navigate = useNavigate();

    const handleFormSubmit = async (e) => {
        e.preventDefault()
        if (!name || !alamat || !nomorHp) {
            alert('<PERSON>hon lengkapi semua field')
            return;
        }

        if (cartItems.length === 0) {
            alert('Keranjang belanja kosong')
            return;
        }

        setIsSubmitting(true);

        try {
            const orderData = {
                customerInfo: {
                    name,
                    alamat,
                    nomorHp
                },
                items: cartItems,
                total: getCartTotal(),
                paymentMethod,
                orderNumber: `ORD-${Date.now()}`
            };

            const newOrder = await createOrder(orderData);
            clearCart();
            alert('Pesanan berhasil dibuat!');
            navigate(`/order-tracking/${newOrder.id}`);
        } catch (error) {
            console.error('Error creating order:', error);
            alert('Gagal membuat pesanan. Silakan coba lagi.');
        } finally {
            setIsSubmitting(false);
        }
    }
    return (
        <div className="space-y-8">
            {/* Order Summary Card */}
            <div className="relative overflow-hidden bg-gradient-to-br from-white to-amber-50/30 border border-amber-200/50 rounded-2xl shadow-lg backdrop-blur-sm">
                <div className="absolute inset-0 bg-gradient-to-br from-amber-500/5 to-orange-500/5"></div>
                <div className="relative p-8">
                    <div className="flex items-center gap-3 mb-6">
                        <div className="w-10 h-10 bg-gradient-to-br from-amber-500 to-orange-500 rounded-xl flex items-center justify-center">
                            <span className="text-white text-lg">🛒</span>
                        </div>
                        <h2 className="text-2xl font-bold bg-gradient-to-r from-amber-700 to-orange-700 bg-clip-text text-transparent">
                            Detail Pesanan
                        </h2>
                    </div>

                    {cartItems.length === 0 ? (
                        <div className="text-center py-12">
                            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span className="text-2xl text-gray-400">🛍️</span>
                            </div>
                            <p className="text-gray-500 font-medium">Keranjang belanja kosong</p>
                            <p className="text-sm text-gray-400 mt-1">Tambahkan produk untuk melanjutkan</p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {cartItems.map((item, index) => (
                                <div key={item.id} className="group relative">
                                    <div className="flex items-center gap-4 p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/80 transition-all duration-300 hover:shadow-md">
                                        <div className="w-12 h-12 bg-gradient-to-br from-amber-100 to-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                            <span className="text-lg font-bold text-amber-700">{index + 1}</span>
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <h3 className="font-semibold text-gray-900 truncate">{item.name}</h3>
                                            <div className="flex items-center gap-2 mt-1">
                                                <span className="inline-flex items-center px-2 py-1 rounded-md bg-amber-100 text-amber-800 text-xs font-medium">
                                                    Qty: {item.quantity}
                                                </span>
                                                <span className="text-sm text-gray-500">
                                                    @ Rp {item.price.toLocaleString('id-ID')}
                                                </span>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="font-bold text-gray-900 text-lg">
                                                Rp {(item.price * item.quantity).toLocaleString('id-ID')}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            ))}

                            <div className="mt-6 pt-6 border-t border-amber-200">
                                <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl p-6 border border-amber-200/50">
                                    <div className="flex justify-between items-center">
                                        <span className="text-lg font-semibold text-gray-700">Total Pembayaran</span>
                                        <span className="text-3xl font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
                                            Rp {getCartTotal().toLocaleString('id-ID')}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Customer Information Form */}
            <div className="relative overflow-hidden bg-gradient-to-br from-white to-blue-50/30 border border-blue-200/50 rounded-2xl shadow-lg backdrop-blur-sm">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5"></div>
                <form onSubmit={handleFormSubmit} className="relative p-8">
                    <div className="flex items-center gap-3 mb-8">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center">
                            <span className="text-white text-lg">📋</span>
                        </div>
                        <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent">
                            Informasi Pengiriman
                        </h2>
                    </div>

                    <div className="space-y-6">
                        {/* Name Field */}
                        <div className="space-y-2">
                            <label htmlFor="name" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                                <span className="text-blue-500">👤</span>
                                Nama Lengkap
                            </label>
                            <div className="relative">
                                <input
                                    type="text"
                                    id="name"
                                    value={name}
                                    onChange={(e) => setName(e.target.value)}
                                    className="w-full px-4 py-3 bg-white/70 backdrop-blur-sm border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 placeholder:text-gray-400"
                                    placeholder="Masukkan nama lengkap Anda"
                                    required
                                />
                                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 to-indigo-500/5 pointer-events-none"></div>
                            </div>
                        </div>

                        {/* Address Field */}
                        <div className="space-y-2">
                            <label htmlFor="alamat" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                                <span className="text-blue-500">📍</span>
                                Alamat Lengkap
                            </label>
                            <div className="relative">
                                <textarea
                                    id="alamat"
                                    value={alamat}
                                    onChange={(e) => setAlamat(e.target.value)}
                                    rows="4"
                                    className="w-full px-4 py-3 bg-white/70 backdrop-blur-sm border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 placeholder:text-gray-400 resize-none"
                                    placeholder="Masukkan alamat lengkap untuk pengiriman"
                                    required
                                />
                                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 to-indigo-500/5 pointer-events-none"></div>
                            </div>
                        </div>

                        {/* Phone Field */}
                        <div className="space-y-2">
                            <label htmlFor="nomorHp" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                                <span className="text-blue-500">📱</span>
                                Nomor HP
                            </label>
                            <div className="relative">
                                <input
                                    type="tel"
                                    id="nomorHp"
                                    value={nomorHp}
                                    onChange={(e) => setNomorHp(e.target.value)}
                                    className="w-full px-4 py-3 bg-white/70 backdrop-blur-sm border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 placeholder:text-gray-400"
                                    placeholder="Contoh: 08123456789"
                                    required
                                />
                                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 to-indigo-500/5 pointer-events-none"></div>
                            </div>
                        </div>

                        {/* Payment Method */}
                        <div className="space-y-3">
                            <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                                <span className="text-blue-500">💳</span>
                                Metode Pembayaran
                            </label>
                            <div className="grid grid-cols-1 gap-3">
                                <label className="relative">
                                    <input
                                        type="radio"
                                        name="paymentMethod"
                                        value="cod"
                                        checked={paymentMethod === 'cod'}
                                        onChange={(e) => setPaymentMethod(e.target.value)}
                                        className="sr-only peer"
                                    />
                                    <div className="flex items-center gap-4 p-4 bg-white/70 backdrop-blur-sm border border-gray-200 rounded-xl cursor-pointer transition-all duration-200 peer-checked:border-blue-500 peer-checked:bg-blue-50/50 peer-checked:shadow-md hover:bg-white/90">
                                        <div className="w-5 h-5 rounded-full border-2 border-gray-300 peer-checked:border-blue-500 peer-checked:bg-blue-500 flex items-center justify-center">
                                            <div className="w-2 h-2 rounded-full bg-white opacity-0 peer-checked:opacity-100"></div>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <span className="text-2xl">🚚</span>
                                            <div>
                                                <div className="font-semibold text-gray-900">Cash on Delivery (COD)</div>
                                                <div className="text-sm text-gray-500">Bayar saat pesanan tiba</div>
                                            </div>
                                        </div>
                                    </div>
                                </label>

                                <label className="relative">
                                    <input
                                        type="radio"
                                        name="paymentMethod"
                                        value="transfer"
                                        checked={paymentMethod === 'transfer'}
                                        onChange={(e) => setPaymentMethod(e.target.value)}
                                        className="sr-only peer"
                                    />
                                    <div className="flex items-center gap-4 p-4 bg-white/70 backdrop-blur-sm border border-gray-200 rounded-xl cursor-pointer transition-all duration-200 peer-checked:border-blue-500 peer-checked:bg-blue-50/50 peer-checked:shadow-md hover:bg-white/90">
                                        <div className="w-5 h-5 rounded-full border-2 border-gray-300 peer-checked:border-blue-500 peer-checked:bg-blue-500 flex items-center justify-center">
                                            <div className="w-2 h-2 rounded-full bg-white opacity-0 peer-checked:opacity-100"></div>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <span className="text-2xl">🏦</span>
                                            <div>
                                                <div className="font-semibold text-gray-900">Transfer Bank</div>
                                                <div className="text-sm text-gray-500">Transfer ke rekening toko</div>
                                            </div>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>

                    {/* Submit Button */}
                    <div className="mt-8 pt-6 border-t border-blue-200">
                        <button
                            type="submit"
                            disabled={isSubmitting || cartItems.length === 0}
                            className={`group relative w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-300 transform ${
                                isSubmitting || cartItems.length === 0
                                    ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                    : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98]'
                            }`}
                        >
                            <div className="flex items-center justify-center gap-3">
                                {isSubmitting ? (
                                    <>
                                        <div className="w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                                        <span>Memproses Pesanan...</span>
                                    </>
                                ) : (
                                    <>
                                        <span>🛍️</span>
                                        <span>Buat Pesanan</span>
                                        <span className="group-hover:translate-x-1 transition-transform duration-200">→</span>
                                    </>
                                )}
                            </div>
                            {!isSubmitting && cartItems.length > 0 && (
                                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-400/20 to-indigo-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            )}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    )
}

export default FormPembelian;
