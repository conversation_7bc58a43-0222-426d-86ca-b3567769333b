import { useNavigate } from "react-router-dom";
import { makeUserAdmin } from "../Utils/Admin/createAdmin";
import { auth } from "../Config/firebase";

const MakeAdminPage = () => {
  const navigate = useNavigate();

  const handleMakeAdmin = async () => {
    try {
      const user = auth.currentUser;
      if (user) {
        await makeUserAdmin(user.uid, user.email);
        alert('You have been made admin! Please refresh the page and try accessing admin panel again.');
        navigate('/');
      } else {
        alert('Please login first');
        navigate('/login');
      }
    } catch (error) {
      console.error('Error making user admin:', error);
      alert('Error making user admin. Check console for details.');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 to-orange-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="text-6xl mb-4">👑</div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Become Admin
          </h2>
          <p className="text-gray-600 mb-8">
            This is a testing page to make yourself admin. In production, admin access should be granted by existing admins.
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <span className="text-yellow-500 text-xl mr-2">⚠️</span>
              <span className="font-semibold text-yellow-800 text-sm">Testing Only</span>
            </div>
            <p className="text-xs text-yellow-700 mt-1">
              This functionality is for development/testing purposes only.
            </p>
          </div>

          <div className="space-y-4">
            <button
              onClick={handleMakeAdmin}
              className="w-full bg-amber-600 text-white py-3 px-4 rounded-md hover:bg-amber-700 transition-colors duration-200 font-medium"
            >
              Make Me Admin
            </button>

            <button
              onClick={() => navigate('/')}
              className="w-full bg-gray-200 text-gray-800 py-3 px-4 rounded-md hover:bg-gray-300 transition-colors duration-200 font-medium"
            >
              Back to Home
            </button>
          </div>

          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-semibold text-blue-800 text-sm mb-2">Alternative Methods:</h3>
            <div className="text-xs text-blue-700 space-y-1">
              <p>1. Go to Firebase Console → Firestore Database</p>
              <p>2. Find your user in 'users' collection</p>
              <p>3. Change 'role' field from 'user' to 'admin'</p>
              <p>4. Save and refresh the app</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MakeAdminPage;
