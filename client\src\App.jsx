import './App.css'
import LoginPages from './Pages/LoginPages'
import SignUpPage from './Pages/signUpPage';
import HomePage from './Pages/HomePage'
import ProductsPage from './Pages/ProductsPage'
import CartPage from './Pages/CartPage'
import FormPembelianPage from './Pages/FormPembelianPage'
import OrderTrackingPage from './Pages/OrderTrackingPage'
import AdminDashboardPage from './Pages/admin/AdminDashboardPage'
import AdminProductsPage from './Pages/admin/AdminProductsPage'
import AdminOrdersPage from './Pages/admin/AdminOrdersPage'
import AdminUsersPage from './Pages/admin/AdminUsersPage'
import NoProtectionRoute from './Components/admin/NoProtectionRoute'
import MakeAdminPage from './Pages/MakeAdminPage'
import DebugPage from './Pages/DebugPage'
import { Routes, Route } from "react-router-dom";
import { CartProvider } from './Context/CartContext';
import { OrderProvider } from './Context/OrderContext';
import { AdminProvider } from './Context/AdminContext';
import { UserProvider } from './Context/UserContext';

function App() {
  return (
    <UserProvider>
      <CartProvider>
        <OrderProvider>
          <AdminProvider>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/login" element={<LoginPages />} />
              <Route path="/signup" element={<SignUpPage />} />
              <Route path="/products" element={<ProductsPage />} />
              <Route path="/cart" element={<CartPage />} />
              <Route path="/form-pembelian" element={<FormPembelianPage />} />
              <Route path="/order-tracking/:orderId" element={<OrderTrackingPage />} />
              <Route path="/make-admin" element={<MakeAdminPage />} />
              <Route path="/debug" element={<DebugPage />} />

              {/* Admin Routes - No Protection for Testing */}
              <Route path="/admin" element={
                <NoProtectionRoute>
                  <AdminDashboardPage />
                </NoProtectionRoute>
              } />
              <Route path="/admin/products" element={
                <NoProtectionRoute>
                  <AdminProductsPage />
                </NoProtectionRoute>
              } />
              <Route path="/admin/orders" element={
                <NoProtectionRoute>
                  <AdminOrdersPage />
                </NoProtectionRoute>
              } />
              <Route path="/admin/users" element={
                <NoProtectionRoute>
                  <AdminUsersPage />
                </NoProtectionRoute>
              } />
            </Routes>
          </AdminProvider>
        </OrderProvider>
      </CartProvider>
    </UserProvider>
  )
}

export default App


