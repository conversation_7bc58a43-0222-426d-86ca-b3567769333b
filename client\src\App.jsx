import './App.css'
import LoginPages from './Pages/LoginPages'
import SignUpPage from './Pages/signUpPage';
import HomePage from './Pages/HomePage'
import ProductsPage from './Pages/ProductsPage'
import CartPage from './Pages/CartPage'
import FormPembelianPage from './Pages/FormPembelianPage'
import OrderTrackingPage from './Pages/OrderTrackingPage'
import AdminDashboardPage from './Pages/admin/AdminDashboardPage'
import AdminProductsPage from './Pages/admin/AdminProductsPage'
import AdminOrdersPage from './Pages/admin/AdminOrdersPage'
import AdminUsersPage from './Pages/admin/AdminUsersPage'
import { Routes, Route } from "react-router-dom";
import { CartProvider } from './Context/CartContext';
import { OrderProvider } from './Context/OrderContext';
import { AdminProvider } from './Context/AdminContext';

function App() {
  return (
    <CartProvider>
      <OrderProvider>
        <AdminProvider>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/login" element={<LoginPages />} />
            <Route path="/signup" element={<SignUpPage />} />
            <Route path="/products" element={<ProductsPage />} />
            <Route path="/cart" element={<CartPage />} />
            <Route path="/form-pembelian" element={<FormPembelianPage />} />
            <Route path="/order-tracking/:orderId" element={<OrderTrackingPage />} />

            {/* Admin Routes */}
            <Route path="/admin" element={<AdminDashboardPage />} />
            <Route path="/admin/products" element={<AdminProductsPage />} />
            <Route path="/admin/orders" element={<AdminOrdersPage />} />
            <Route path="/admin/users" element={<AdminUsersPage />} />
          </Routes>
        </AdminProvider>
      </OrderProvider>
    </CartProvider>
  )
}

export default App


